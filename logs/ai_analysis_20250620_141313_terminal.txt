
================================================================================
sMTm Framework - Terminal Log Session
================================================================================
Session ID: ai_analysis_20250620_141313
Start Time: 2025-06-20 14:13:13
Command: /ai-analysis
================================================================================

[14:13:13] [INFO] 🚀 AI Analysis Started for app: Unknown
[14:13:13] [INFO] ============================================================
[14:13:13] [INFO] 📱 AI Analysis - session_start: AI Analysis session started: ai_analysis_20250620_141313
[14:13:13] [INFO] 📋 === AI ANALYSIS SESSION ===
[14:13:13] [INFO]   session_id: ai_analysis_20250620_141313
[14:13:13] [INFO]   start_time: 2025-06-20T14:13:13.623364
[14:13:13] [INFO]   custom_instructions: comprehensive
[14:13:13] [INFO]   analysis_mode: comprehensive_ui_analysis
[14:13:13] [INFO] 📋 === END AI ANALYSIS SESSION ===
[14:13:13] [STDOUT] Detected OS: macOS
[14:13:13] [RICH_CONSOLE] Detected OS: macOS
[14:13:13] [INFO] 📱 AI Analysis - os_detection: Detected OS: macOS
[14:13:13] [INFO] 📋 === OS DETECTION ===
[14:13:13] [INFO]   detected_os: macOS
[14:13:13] [INFO]   detection_method: OSDetector
[14:13:13] [INFO]   timestamp: 2025-06-20T14:13:13.624968
[14:13:13] [INFO] 📋 === END OS DETECTION ===
[14:13:13] [STDOUT] Use existing installation? (y/n):
[14:13:13] [RICH_CONSOLE] Use existing installation? (y/n):
[14:13:15] [INFO] 📱 AI Analysis - installation_preference: Use existing installation: True
[14:13:15] [STDOUT] What mobile OS would you like to analyze?
[14:13:15] [RICH_CONSOLE] What mobile OS would you like to analyze?
[14:13:15] [STDOUT] 1. Android
[14:13:15] [RICH_CONSOLE] 1. Android
[14:13:15] [STDOUT] 2. iOS
[14:13:15] [RICH_CONSOLE] 2. iOS
[14:13:15] [STDOUT] Enter your choice:
[14:13:15] [RICH_CONSOLE] Enter your choice:
[14:13:16] [INFO] 📱 AI Analysis - mobile_os_selection: Selected mobile OS: android
[14:13:16] [STDOUT] 
[14:13:16] [INFO] 📱 AI Analysis - android_setup_start: Starting Android environment setup
[14:13:16] [STDOUT] Checking Android environment...
⠋ Processing command...
[14:13:16] [RICH_CONSOLE] Checking Android environment...
[14:13:16] [STDOUT] 
Android emulator is already running
⠋ Processing command...
[14:13:16] [RICH_CONSOLE] Android emulator is already running
[14:13:16] [STDOUT] 
Connecting to Android device...
⠋ Processing command...
[14:13:16] [RICH_CONSOLE] Connecting to Android device...
[14:13:16] [STDOUT] Connecting to Android device...
[14:13:16] [STDOUT] 🔍 Validating device readiness: emulator-5554
[14:13:16] [STDOUT]   📡 Testing basic shell connectivity (attempt 1/3, timeout: 8s)...
[14:13:16] [STATUS] Processing command...
[14:13:16] [STDOUT]   ✅ Shell connectivity OK (attempt 1)
[14:13:16] [STDOUT]   🚀 Checking boot completion status...
[14:13:16] [STDOUT]   ✅ Device fully booted
[14:13:16] [STDOUT]   📱 Testing device responsiveness...
[14:13:16] [STDOUT]   ✅ Device responsive (Android 14)
[14:13:16] [STDOUT] 🎉 Device emulator-5554 is ready for connection!
[14:13:16] [STDOUT] ✅ 1 device(s) fully ready
[14:13:16] [STDOUT] 📱 Found 1 device(s)
[14:13:16] [STDOUT]   Device 1: emulator-5554 (status: device)
[14:13:16] [STDOUT] 🔗 Attempting to connect to device: emulator-5554
[14:13:16] [STDOUT] 🔧 Preparing device for connection...
[14:13:19] [STDOUT] 🔄 Trying direct connection strategy (timeout: 20s)...
[14:13:19] [STDOUT]   🔌 Attempting uiautomator2 connection...
[14:13:20] [STDOUT]   🧪 Verifying connection...
[14:13:20] [STDOUT] ✓ Device connection established using direct strategy
[14:13:20] [STDOUT]   📱 Device: sdk_gphone64_arm64
[14:13:20] [STDOUT] 
✓ Android Hardware Button Controller initialized
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] ✓ Android Hardware Button Controller initialized
[14:13:20] [STDOUT] 
✓ Android environment setup completed!
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] ✓ Android environment setup completed!
[14:13:20] [INFO] 📋 === ANDROID SETUP SUCCESS ===
[14:13:20] [INFO]   setup_duration: 3.26s
[14:13:20] [INFO]   device_connected: False
[14:13:20] [INFO]   emulator_status: unknown
[14:13:20] [INFO]   android_version: unknown
[14:13:20] [INFO] 📋 === END ANDROID SETUP SUCCESS ===
[14:13:20] [INFO] 📱 AI Analysis - android_setup_success: Android environment setup completed
[14:13:20] [INFO] 📱 AI Analysis - app_selection_start: Starting app selection and preparation
[14:13:20] [STDOUT] 📱 Analyzing available applications...
[14:13:20] [RICH_CONSOLE] 📱 Analyzing available applications...
[14:13:20] [STDOUT] 📋 Available applications:
[14:13:20] [RICH_CONSOLE] 📋 Available applications:
[14:13:20] [STDOUT]   1. com.kemendikdasmen.rumahpendidikan (Installed)
[14:13:20] [RICH_CONSOLE]   1. com.kemendikdasmen.rumahpendidikan (Installed)
[14:13:20] [STDOUT]   2. Rumah Pendidikan (APK File)
[14:13:20] [RICH_CONSOLE]   2. Rumah Pendidikan (APK File)
[14:13:20] [STDOUT] 🎯 Auto-selected main application for custom analysis: com.kemendikdasmen.rumahpendidikan
[14:13:20] [RICH_CONSOLE] 🎯 Auto-selected main application for custom analysis: com.kemendikdasmen.rumahpendidikan
[14:13:20] [STDOUT] ✓ Selected: com.kemendikdasmen.rumahpendidikan
[14:13:20] [RICH_CONSOLE] ✓ Selected: com.kemendikdasmen.rumahpendidikan
[14:13:20] [STDOUT] 🔍 Checking if com.kemendikdasmen.rumahpendidikan is already running...
[14:13:20] [RICH_CONSOLE] 🔍 Checking if com.kemendikdasmen.rumahpendidikan is already running...
[14:13:20] [STDOUT] ✓ com.kemendikdasmen.rumahpendidikan is already running and ready for analysis!
[14:13:20] [RICH_CONSOLE] ✓ com.kemendikdasmen.rumahpendidikan is already running and ready for analysis!
[14:13:20] [STDOUT] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[14:13:20] [RICH_CONSOLE] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[14:13:20] [STDOUT] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[14:13:20] [RICH_CONSOLE] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[14:13:20] [STDOUT] 🚀 Skipping launch - proceeding directly to analysis
[14:13:20] [RICH_CONSOLE] 🚀 Skipping launch - proceeding directly to analysis
[14:13:20] [STDOUT] ✓ com.kemendikdasmen.rumahpendidikan is ready for analysis!
[14:13:20] [RICH_CONSOLE] ✓ com.kemendikdasmen.rumahpendidikan is ready for analysis!
[14:13:20] [INFO] 📱 AI Analysis - app_selected: Selected app: com.kemendikdasmen.rumahpendidikan
[14:13:20] [INFO] 📱 AI Analysis - custom_instructions: Custom analysis: comprehensive
[14:13:20] [STDOUT] 🎯 Custom Analysis Instructions: comprehensive
[14:13:20] [RICH_CONSOLE] 🎯 Custom Analysis Instructions: comprehensive
[14:13:20] [INFO] 🚀 AI Analysis Started for app: com.kemendikdasmen.rumahpendidikan
[14:13:20] [INFO] ============================================================
[14:13:20] [STDOUT] 
[14:13:20] [INFO] 📱 AI Analysis - ui_analysis_start: Starting deep UI analysis
[14:13:20] [INFO] 📋 === UI ANALYSIS CONFIGURATION ===
[14:13:20] [INFO]   custom_instructions: comprehensive
[14:13:20] [INFO]   target_elements: 3000
[14:13:20] [INFO]   analysis_mode: comprehensive
[14:13:20] [INFO]   app_package: com.kemendikdasmen.rumahpendidikan
[14:13:20] [INFO] 📋 === END UI ANALYSIS CONFIGURATION ===
[14:13:20] [STDOUT] 🔍 Starting sequential, organized UI analysis...
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] 🔍 Starting sequential, organized UI analysis...
[14:13:20] [STDOUT] 
🔧 Initializing AI improvement system...
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] 🔧 Initializing AI improvement system...
[14:13:20] [STDOUT] 
✅ Professional Hybrid Headless Detector initialized
⠋ Processing command...
[14:13:20] [STDOUT] 
✅ Enhanced Element Classifier with Mobile Locator Knowledge initialized
⠋ Processing command...
[14:13:20] [STDOUT] 
✅ Real-time Problem Solver initialized
⠋ Processing command...
[14:13:20] [STDOUT] 
✅ AI Self-Improvement System initialized successfully
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] ✅ AI Self-Improvement System initialized successfully
[14:13:20] [STDOUT] 
🔧 AI improvement system initialization completed
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] 🔧 AI improvement system initialization completed
[14:13:20] [STDOUT] 
✅ AI improvement system initialized successfully
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] ✅ AI improvement system initialized successfully
[14:13:20] [STDOUT] 
🔍 robust_element_collector type: <class 'mobile.robust_element_collector.RobustElementCollector'>
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] 🔍 robust_element_collector type: <class 'mobile.robust_element_collector.RobustElementCollector'>
[14:13:20] [STDOUT] 
✅ collect_all_elements_robustly method exists on analyzer
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] ✅ collect_all_elements_robustly method exists on analyzer
[14:13:20] [STDOUT] 
🔍 Method type: <class 'method'>
⠋ Processing command...
[14:13:20] [RICH_CONSOLE] 🔍 Method type: <class 'method'>
[14:13:20] [STDOUT] 
📱 Current app: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[14:13:20] [RICH_CONSOLE] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[14:13:20] [STDOUT] 
🎯 Target app: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[14:13:20] [RICH_CONSOLE] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[14:13:20] [STDOUT] 
✅ Verified: Analysis starting in correct main app
⠙ Processing command...
[14:13:20] [RICH_CONSOLE] ✅ Verified: Analysis starting in correct main app
[14:13:20] [STDOUT] 
🧹 Checking for corrupted Excel files (JSON with .xlsx extension)...
⠙ Processing command...
[14:13:20] [RICH_CONSOLE] 🧹 Checking for corrupted Excel files (JSON with .xlsx extension)...
[14:13:20] [STDOUT] 
✅ No corrupted Excel files found
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] ✅ No corrupted Excel files found
[14:13:20] [INFO] 📋 === LOOP PREVENTION SYSTEM RESET ===
[14:13:20] [INFO]   emergency_exit_triggered: False
[14:13:20] [INFO]   analysis_start_time: 2025-06-20T14:13:20.624214
[14:13:20] [INFO]   timeout_minutes: 45
[14:13:20] [INFO]   consecutive_failures_threshold: 8
[14:13:20] [INFO]   reset_timestamp: 2025-06-20T14:13:20.624222
[14:13:20] [INFO] 📋 === END LOOP PREVENTION SYSTEM RESET ===
[14:13:20] [STDOUT] 
✅ Loop prevention system reset for new analysis session
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] ✅ Loop prevention system reset for new analysis session
[14:13:20] [STDOUT] 
🎯 COMPREHENSIVE MODE TRIGGERED: AI will autonomously discover ALL UI structure
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] 🎯 COMPREHENSIVE MODE TRIGGERED: AI will autonomously discover ALL UI structure
[14:13:20] [STDOUT] 
🧠 TEACHING AI: Collect complete hierarchical navigation structure without hardcoded values
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] 🧠 TEACHING AI: Collect complete hierarchical navigation structure without hardcoded values
[14:13:20] [STDOUT] 
🎯 COMPREHENSIVE MODE ACTIVATED: Collecting ALL element locators from complete UI structure
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] 🎯 COMPREHENSIVE MODE ACTIVATED: Collecting ALL element locators from complete UI structure
[14:13:20] [STDOUT] 
📋 AI will autonomously discover and analyze entire application hierarchy
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] 📋 AI will autonomously discover and analyze entire application hierarchy
[14:13:20] [STDOUT] 
🎯 Target: Complete menu structure, sub-menus, and all navigation elements
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] 🎯 Target: Complete menu structure, sub-menus, and all navigation elements
[14:13:20] [STDOUT] 
🚀 ENHANCED: 3000+ element collection with 60-minute duration
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] 🚀 ENHANCED: 3000+ element collection with 60-minute duration
[14:13:20] [STDOUT] 
📊 Performance Goals: 3000+ elements, <3% unknown, 60 minutes max
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] 📊 Performance Goals: 3000+ elements, <3% unknown, 60 minutes max
[14:13:20] [STDOUT] 
🏗️ HIERARCHICAL STRUCTURE TARGET:
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] 🏗️ HIERARCHICAL STRUCTURE TARGET:
[14:13:20] [STDOUT] 
   • Beranda with all subsections (1.1-1.15)
⠹ Processing command...
[14:13:20] [RICH_CONSOLE]    • Beranda with all subsections (1.1-1.15)
[14:13:20] [STDOUT] 
   • Ruang GTK with all sub-elements (1.4.1-1.4.19)
⠹ Processing command...
[14:13:20] [RICH_CONSOLE]    • Ruang GTK with all sub-elements (1.4.1-1.4.19)
[14:13:20] [STDOUT] 
   • Ruang Murid, Ruang Sekolah, Ruang Bahasa, etc.
⠹ Processing command...
[14:13:20] [RICH_CONSOLE]    • Ruang Murid, Ruang Sekolah, Ruang Bahasa, etc.
[14:13:20] [STDOUT] 
   • Complete navigation bar elements
⠹ Processing command...
[14:13:20] [RICH_CONSOLE]    • Complete navigation bar elements
[14:13:20] [STDOUT] 
   • External app first-page elements only
⠹ Processing command...
[14:13:20] [RICH_CONSOLE]    • External app first-page elements only
[14:13:20] [STDOUT] 
⚠️ SKIPPING INCREMENTAL ANALYSIS - Full comprehensive analysis will be performed
⠹ Processing command...
[14:13:20] [RICH_CONSOLE] ⚠️ SKIPPING INCREMENTAL ANALYSIS - Full comprehensive analysis will be performed
[14:13:20] [STDOUT] 
🎯 Primary app focus: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[14:13:20] [RICH_CONSOLE] 🎯 Primary app focus: com.kemendikdasmen.rumahpendidikan
[14:13:20] [STDOUT] 
🔒 STRICT FILTERING: Only com.kemendikdasmen.rumahpendidikan and direct external apps allowed
⠸ Processing command...
[14:13:20] [RICH_CONSOLE] 🔒 STRICT FILTERING: Only com.kemendikdasmen.rumahpendidikan and direct external apps allowed
[14:13:20] [STDOUT] 
🎯 Main app context set: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[14:13:20] [STDOUT] 
🧠 Intelligent navigation system initialized
⠸ Processing command...
[14:13:20] [RICH_CONSOLE] 🧠 Intelligent navigation system initialized
[14:13:20] [STDOUT] 
📂 Loading existing elements from persistent file: rumahpendidikan_persistent_analysis.xlsx
⠸ Processing command...
[14:13:20] [STDOUT] 
✅ Loaded 229 existing elements from persistent file
⠦ Processing command...
[14:13:20] [STDOUT] 
✅ Persistent Excel manager initialized for com.kemendikdasmen.rumahpendidikan
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] ✅ Persistent Excel manager initialized for com.kemendikdasmen.rumahpendidikan
[14:13:20] [STDOUT] 
✅ Persistent Excel file is valid: rumahpendidikan_persistent_analysis.xlsx
⠦ Processing command...
[14:13:20] [STDOUT] 
📋 Using persistent Excel storage only - no separate live save files created
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 📋 Using persistent Excel storage only - no separate live save files created
[14:13:20] [STDOUT] 
🎯 Performance Goals Tracking Started:
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 🎯 Performance Goals Tracking Started:
[14:13:20] [STDOUT] 
  • Target Elements: 3000
⠦ Processing command...
[14:13:20] [RICH_CONSOLE]   • Target Elements: 3000
[14:13:20] [STDOUT] 
  • Target Duration: 60 minutes
⠦ Processing command...
[14:13:20] [RICH_CONSOLE]   • Target Duration: 60 minutes
[14:13:20] [STDOUT] 
  • Max Unknown Elements: 3.0%
⠦ Processing command...
[14:13:20] [RICH_CONSOLE]   • Max Unknown Elements: 3.0%
[14:13:20] [STDOUT] 
🔍 Real-time problem monitoring started in background
⠦ Processing command...
[14:13:20] [STDOUT] 
🔧 Real-time problem solver monitoring started
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 🔧 Real-time problem solver monitoring started
[14:13:20] [STDOUT] 
🔍 Searching for previous analysis state for rumahpendidikan...
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 🔍 Searching for previous analysis state for rumahpendidikan...
[14:13:20] [STDOUT] 
📋 Checking persistent Excel manager for analysis state...
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 📋 Checking persistent Excel manager for analysis state...
[14:13:20] [STDOUT] 
⚠️ Could not read Analysis_State from persistent file: Worksheet named 'Analysis_State' not found
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] ⚠️ Could not read Analysis_State from persistent file: Worksheet named 'Analysis_State' not found
[14:13:20] [STDOUT] 
📂 Falling back to search for legacy live analysis files...
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 📂 Falling back to search for legacy live analysis files...
[14:13:20] [STDOUT] 
📂 Found previous analysis state: ./data/analysis/rumahpendidikan_20250602_215601_live_analysis.xlsx
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 📂 Found previous analysis state: ./data/analysis/rumahpendidikan_20250602_215601_live_analysis.xlsx
[14:13:20] [STDOUT] 
⚠️ Could not read file format: 'utf-8' codec can't decode byte 0xb6 in position 11: invalid start byte
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] ⚠️ Could not read file format: 'utf-8' codec can't decode byte 0xb6 in position 11: invalid start byte
[14:13:20] [STDOUT] 
🏗️ HIERARCHICAL STRUCTURE ANALYSIS STARTED
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 🏗️ HIERARCHICAL STRUCTURE ANALYSIS STARTED
[14:13:20] [STDOUT] 
📋 Target: Complete Rumah Pendidikan UI hierarchy collection
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 📋 Target: Complete Rumah Pendidikan UI hierarchy collection
[14:13:20] [STDOUT] 
📋 Phase 1: Comprehensive main page analysis and feature identification...
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 📋 Phase 1: Comprehensive main page analysis and feature identification...
[14:13:20] [STDOUT] 
🔍 Collecting all elements from main page...
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 🔍 Collecting all elements from main page...
[14:13:20] [STDOUT] 
✅ collect_all_elements_robustly method exists
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] ✅ collect_all_elements_robustly method exists
[14:13:20] [STDOUT] 
🔍 Method type: <class 'method'>
⠦ Processing command...
[14:13:20] [RICH_CONSOLE] 🔍 Method type: <class 'method'>
[14:13:21] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 1/3)
[14:13:21] [ERROR] ❌ Error Type: Exception
[14:13:21] [ERROR] ❌ Stack Trace:
[14:13:21] [ERROR]     Traceback (most recent call last):
[14:13:21] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:21] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:21] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:21] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:21] [ERROR]         return await fut
[14:13:21] [ERROR]                ^^^^^^^^^
[14:13:21] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:22] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 2/3)
[14:13:22] [ERROR] ❌ Error Type: Exception
[14:13:22] [ERROR] ❌ Stack Trace:
[14:13:22] [ERROR]     Traceback (most recent call last):
[14:13:22] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:22] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:22] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:22] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:22] [ERROR]         return await fut
[14:13:22] [ERROR]                ^^^^^^^^^
[14:13:22] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:23] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 3/3)
[14:13:23] [ERROR] ❌ Error Type: Exception
[14:13:23] [ERROR] ❌ Stack Trace:
[14:13:23] [ERROR]     Traceback (most recent call last):
[14:13:23] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:23] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:23] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:23] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:23] [ERROR]         return await fut
[14:13:23] [ERROR]                ^^^^^^^^^
[14:13:23] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:23] [ERROR] ❌ ERROR in element collection: Failed to dump UI hierarchy
[14:13:23] [ERROR] ❌ Error Type: Exception
[14:13:23] [ERROR] ❌ Stack Trace:
[14:13:23] [ERROR]     NoneType: None
[14:13:24] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 1/3)
[14:13:24] [ERROR] ❌ Error Type: Exception
[14:13:24] [ERROR] ❌ Stack Trace:
[14:13:24] [ERROR]     Traceback (most recent call last):
[14:13:24] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:24] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:24] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:24] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:24] [ERROR]         return await fut
[14:13:24] [ERROR]                ^^^^^^^^^
[14:13:24] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:25] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 2/3)
[14:13:25] [ERROR] ❌ Error Type: Exception
[14:13:25] [ERROR] ❌ Stack Trace:
[14:13:25] [ERROR]     Traceback (most recent call last):
[14:13:25] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:25] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:25] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:25] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:25] [ERROR]         return await fut
[14:13:25] [ERROR]                ^^^^^^^^^
[14:13:25] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:26] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 3/3)
[14:13:26] [ERROR] ❌ Error Type: Exception
[14:13:26] [ERROR] ❌ Stack Trace:
[14:13:26] [ERROR]     Traceback (most recent call last):
[14:13:26] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:26] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:26] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:26] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:26] [ERROR]         return await fut
[14:13:26] [ERROR]                ^^^^^^^^^
[14:13:26] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:26] [ERROR] ❌ ERROR in element collection: Failed to dump UI hierarchy
[14:13:26] [ERROR] ❌ Error Type: Exception
[14:13:26] [ERROR] ❌ Stack Trace:
[14:13:26] [ERROR]     NoneType: None
[14:13:28] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 1/3)
[14:13:28] [ERROR] ❌ Error Type: Exception
[14:13:28] [ERROR] ❌ Stack Trace:
[14:13:28] [ERROR]     Traceback (most recent call last):
[14:13:28] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:28] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:28] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:28] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:28] [ERROR]         return await fut
[14:13:28] [ERROR]                ^^^^^^^^^
[14:13:28] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:29] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 2/3)
[14:13:29] [ERROR] ❌ Error Type: Exception
[14:13:29] [ERROR] ❌ Stack Trace:
[14:13:29] [ERROR]     Traceback (most recent call last):
[14:13:29] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:29] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:29] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:29] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:29] [ERROR]         return await fut
[14:13:29] [ERROR]                ^^^^^^^^^
[14:13:29] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:30] [ERROR] ❌ ERROR in device: Hierarchy dump failed: object str can't be used in 'await' expression (attempt 3/3)
[14:13:30] [ERROR] ❌ Error Type: Exception
[14:13:30] [ERROR] ❌ Stack Trace:
[14:13:30] [ERROR]     Traceback (most recent call last):
[14:13:30] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1382, in _safe_dump_hierarchy
[14:13:30] [ERROR]         hierarchy = await asyncio.wait_for(
[14:13:30] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[14:13:30] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[14:13:30] [ERROR]         return await fut
[14:13:30] [ERROR]                ^^^^^^^^^
[14:13:30] [ERROR]     TypeError: object str can't be used in 'await' expression
[14:13:30] [ERROR] ❌ ERROR in element collection: Failed to dump UI hierarchy
[14:13:30] [ERROR] ❌ Error Type: Exception
[14:13:30] [ERROR] ❌ Stack Trace:
[14:13:30] [ERROR]     NoneType: None
[14:13:30] [STDOUT] 
❌ No elements found on main page
⠴ Processing command...
[14:13:30] [RICH_CONSOLE] ❌ No elements found on main page
[14:13:30] [ERROR] ❌ ERROR in Deep UI Analysis: No elements found on main page
[14:13:30] [ERROR] ❌ Error Type: Exception
[14:13:30] [ERROR] ❌ Stack Trace:
[14:13:30] [ERROR]     NoneType: None
[14:13:30] [ERROR] ❌ Additional Context:
[14:13:30] [ERROR]     error_message: No elements found on main page
[14:13:30] [ERROR]     analysis_duration: 10.00s
[14:13:30] [ERROR]     elements_found_before_failure: 0
[14:13:30] [ERROR]     step: deep_ui_analysis
[14:13:30] [INFO] 📱 AI Analysis - ui_analysis_failed: UI analysis failed: No elements found on main page
[14:13:30] [INFO] ============================================================
[14:13:30] [ERROR] ❌ AI Analysis Failed: No elements found on main page
[14:13:30] [INFO] End Time: 2025-06-20 14:13:30

================================================================================
Session End Time: 2025-06-20 14:13:30
Log File: logs/ai_analysis_20250620_141313_terminal.txt
================================================================================
