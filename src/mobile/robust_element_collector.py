"""
Robust Element Collector - Handles unstable apps with checkpoint-based progressive collection
Designed to work around app crashes and stability issues while maintaining quality
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from rich.console import Console
import logging

from storage.persistent_excel_manager import PersistentExcelManager

console = Console()
logger = logging.getLogger(__name__)


class RobustElementCollector:
    """
    Robust element collector that handles unstable apps through:
    1. Checkpoint-based progressive collection
    2. Multiple collection strategies
    3. Crash recovery with state preservation
    4. Quality-first approach with fallback mechanisms
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.checkpoints = []
        self.collected_elements = {}
        self.collection_state = {
            "current_section": None,
            "sections_completed": [],
            "elements_found": 0,
            "last_checkpoint": None,
            "recovery_count": 0
        }
        
        # Initialize managers
        self.persistent_excel_manager = None
        
        # Collection strategies (ordered by robustness)
        self.collection_strategies = [
            self._strategy_screenshot_based_collection,
            self._strategy_xml_hierarchy_collection,
            self._strategy_incremental_element_collection,
            self._strategy_emergency_collection
        ]
        
        # Menu structure mapping for <PERSON><PERSON><PERSON>
        self.menu_structure = self._initialize_menu_structure()
        
    def _initialize_menu_structure(self) -> Dict[str, Any]:
        """Initialize the expected menu structure for systematic collection"""
        return {
            "main_sections": [
                {
                    "name": "Beranda",
                    "type": "main_page",
                    "elements": ["Rumah Pendidikan", "Jelajahi Beragam Layanan", "Search"]
                },
                {
                    "name": "Ruang GTK",
                    "type": "menu_section",
                    "sub_items": [
                        "Diklat", "Sertifikat Pendidik", "Pelatihan Mandiri", "Komunitas",
                        "Pengelolaan Kinerja", "Seleksi kepala sekolah", "Refleksi kompetensi",
                        "Perangkat ajar", "cp/atp", "Ide praktik", "Bukti karya", "video inspirasi",
                        "Asesmen", "Kelas", "Pengelolaan Pembelajaran", "Peningkatan kompetensi",
                        "Pengelolaan satuan pendidikan"
                    ]
                },
                {
                    "name": "Ruang Murid",
                    "type": "menu_section",
                    "sub_items": [
                        "Sumber belajar", "Buku bacaan digital", "7 kebiasaan anak indonesia hebat",
                        "Album lagu anak", "Bank soal", "Riwayat pendidikan", "Akun pendidikan"
                    ]
                },
                {
                    "name": "Ruang sekolah",
                    "type": "menu_section",
                    "sub_items": [
                        "Profil sekolah", "Rapor satuan pendidikan", "Rencana kegiatan dan belanja sekolah",
                        "Bantuan operasional", "Akun pendidikan"
                    ]
                },
                {
                    "name": "Ruang bahasa",
                    "type": "menu_section",
                    "sub_items": [
                        "Kamus bahasa", "Penerjemahan daring", "Layanan UKBI", "BIPA Daring"
                    ]
                },
                {
                    "name": "Ruang pemerintah",
                    "type": "menu_section",
                    "sub_items": [
                        "Neraca pendidikan daerah", "Rapor pendidikan daerah", "Akun pendidikan"
                    ]
                },
                {
                    "name": "Ruang mitra",
                    "type": "menu_section",
                    "sub_items": [
                        "Publikasi ilmiah", "Mitra barjas pendidikan", "Kemitraan pendidikan", "Relawan pendidikan"
                    ]
                },
                {
                    "name": "Ruang Publik",
                    "type": "menu_section",
                    "sub_items": [
                        "Pusat Perbukaan", "Majalah Pendidikan", "Bantuan Pendidikan",
                        "Kursus Digital", "Layanan Informasi dan Pengaduan", "Informasi Data Pendidikan"
                    ]
                },
                {
                    "name": "Ruang Orang Tua",
                    "type": "menu_section",
                    "sub_items": [
                        "Layanan Informasi dan Pengaduan", "Pengaduan Pendampingan", "Konsultasi Pendidikan"
                    ]
                }
            ],
            "navigation_elements": [
                "Beranda", "Cari", "Pemberitahuan", "Akun", "Pusat Bantuan",
                "Tentang Aplikasi", "Kebijakan Privasi", "Syarat dan Ketentuan", "Keluar"
            ],
            "special_sections": [
                "Layanan Paling banyak diakses", "Lihat Semua", "Butuh Bantuan"
            ]
        }
    
    async def collect_all_elements_robustly(self, device, app_package: str, 
                                          persistent_excel_manager: PersistentExcelManager) -> Dict[str, Any]:
        """
        Main method to collect all elements robustly despite app instability
        """
        try:
            console.print("[cyan]🛡️ ROBUST ELEMENT COLLECTION STARTED[/cyan]")
            console.print("[cyan]📋 Strategy: Checkpoint-based progressive collection with crash recovery[/cyan]")
            
            self.persistent_excel_manager = persistent_excel_manager
            
            # Initialize collection state
            await self._initialize_collection_state(app_package)
            
            # Start progressive collection
            collection_result = await self._progressive_collection_with_checkpoints(device, app_package)
            
            # Final verification and quality check
            final_result = await self._verify_collection_completeness(collection_result)
            
            console.print(f"[green]✅ ROBUST COLLECTION COMPLETED: {final_result['total_elements']} elements[/green]")
            return final_result
            
        except Exception as e:
            logger.error(f"Robust element collection failed: {e}")
            return await self._emergency_collection_fallback(device, app_package)
    
    async def _initialize_collection_state(self, app_package: str):
        """Initialize collection state and load any existing progress"""
        try:
            console.print("[cyan]📋 Initializing robust collection state...[/cyan]")
            
            # Load existing elements from persistent storage
            if self.persistent_excel_manager:
                existing_count = len(self.persistent_excel_manager.existing_elements)
                console.print(f"[cyan]📊 Found {existing_count} existing elements in persistent storage[/cyan]")
                
            # Initialize checkpoint system
            self.collection_state = {
                "app_package": app_package,
                "start_time": datetime.now().isoformat(),
                "current_section": None,
                "sections_completed": [],
                "elements_found": existing_count if self.persistent_excel_manager else 0,
                "last_checkpoint": None,
                "recovery_count": 0,
                "collection_strategy": "progressive_with_checkpoints"
            }
            
            console.print("[green]✅ Collection state initialized[/green]")
            
        except Exception as e:
            logger.error(f"Failed to initialize collection state: {e}")
            raise
    
    async def _progressive_collection_with_checkpoints(self, device, app_package: str) -> Dict[str, Any]:
        """
        Progressive collection with checkpoint-based recovery
        """
        try:
            console.print("[cyan]🎯 Starting progressive collection with checkpoints...[/cyan]")
            
            total_elements_collected = 0
            sections_completed = 0
            
            # Process each main section systematically
            for section_index, section in enumerate(self.menu_structure["main_sections"]):
                try:
                    console.print(f"[yellow]📋 SECTION {section_index + 1}/{len(self.menu_structure['main_sections'])}: {section['name']}[/yellow]")
                    
                    # Create checkpoint before section
                    checkpoint = await self._create_checkpoint(device, section, section_index)
                    
                    # Collect elements from this section
                    section_result = await self._collect_section_elements_robustly(device, section, app_package)
                    
                    if section_result["success"]:
                        total_elements_collected += section_result["elements_collected"]
                        sections_completed += 1
                        self.collection_state["sections_completed"].append(section["name"])
                        
                        console.print(f"[green]✅ Section '{section['name']}' completed: {section_result['elements_collected']} elements[/green]")
                    else:
                        console.print(f"[yellow]⚠️ Section '{section['name']}' partially completed: {section_result.get('elements_collected', 0)} elements[/yellow]")
                        
                        # Try recovery if section failed
                        recovery_result = await self._attempt_section_recovery(device, section, checkpoint)
                        if recovery_result["success"]:
                            total_elements_collected += recovery_result["elements_collected"]
                            sections_completed += 1
                    
                    # Save progress after each section
                    await self._save_progress_checkpoint(total_elements_collected, sections_completed)
                    
                except Exception as e:
                    logger.warning(f"Section {section['name']} failed: {e}")
                    # Continue with next section
                    continue
            
            # Collect navigation elements
            nav_result = await self._collect_navigation_elements_robustly(device, app_package)
            total_elements_collected += nav_result.get("elements_collected", 0)
            
            return {
                "success": True,
                "total_elements": total_elements_collected,
                "sections_completed": sections_completed,
                "collection_strategy": "progressive_with_checkpoints",
                "recovery_count": self.collection_state["recovery_count"]
            }
            
        except Exception as e:
            logger.error(f"Progressive collection failed: {e}")
            return await self._emergency_collection_fallback(device, app_package)

    async def _collect_section_elements_robustly(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Robustly collect elements from a specific section with multiple strategies
        """
        try:
            section_name = section["name"]
            console.print(f"[cyan]🔍 Collecting elements from section: {section_name}[/cyan]")

            elements_collected = 0
            strategies_tried = 0

            # Try each collection strategy until successful
            for strategy_index, strategy in enumerate(self.collection_strategies):
                try:
                    console.print(f"[cyan]📊 Strategy {strategy_index + 1}/{len(self.collection_strategies)}: {strategy.__name__}[/cyan]")

                    strategy_result = await strategy(device, section, app_package)
                    strategies_tried += 1

                    if strategy_result["success"] and strategy_result["elements_collected"] > 0:
                        elements_collected = strategy_result["elements_collected"]
                        console.print(f"[green]✅ Strategy successful: {elements_collected} elements collected[/green]")
                        break
                    else:
                        console.print(f"[yellow]⚠️ Strategy {strategy.__name__} found {strategy_result.get('elements_collected', 0)} elements[/yellow]")

                except Exception as e:
                    logger.warning(f"Collection strategy {strategy.__name__} failed: {e}")
                    continue

            return {
                "success": elements_collected > 0,
                "elements_collected": elements_collected,
                "strategies_tried": strategies_tried,
                "section_name": section_name
            }

        except Exception as e:
            logger.error(f"Section collection failed for {section.get('name', 'Unknown')}: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _strategy_screenshot_based_collection(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Strategy 1: Screenshot-based element collection (most robust for unstable apps)
        """
        try:
            console.print("[cyan]📸 Using screenshot-based collection strategy[/cyan]")

            elements_found = 0

            try:
                # Get current screen elements using multiple selectors
                selectors = [
                    "//*[@clickable='true']",
                    "//*[@enabled='true']",
                    "//*[string-length(@text)>0]",
                    "//*[@resource-id]",
                    "//*"
                ]

                for selector in selectors:
                    try:
                        elements = device.xpath(selector).all()
                        if elements:
                            # Process and save elements
                            processed_elements = await self._process_elements_for_section(elements, section, app_package)
                            elements_found += len(processed_elements)

                            console.print(f"[green]✅ Selector '{selector}' found {len(processed_elements)} elements[/green]")
                            break
                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {e}")
                        continue

                return {
                    "success": elements_found > 0,
                    "elements_collected": elements_found,
                    "strategy": "screenshot_based"
                }

            except Exception as e:
                logger.warning(f"Screenshot analysis failed: {e}")
                return {"success": False, "elements_collected": 0, "error": str(e)}

        except Exception as e:
            logger.error(f"Screenshot-based collection failed: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _strategy_xml_hierarchy_collection(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Strategy 2: XML hierarchy-based collection (good for stable moments)
        """
        try:
            console.print("[cyan]🌳 Using XML hierarchy collection strategy[/cyan]")

            elements_found = 0

            try:
                # Get XML hierarchy
                xml_hierarchy = device.dump_hierarchy()

                if xml_hierarchy:
                    # Parse XML and extract elements
                    elements = await self._parse_xml_hierarchy(xml_hierarchy, section)

                    if elements:
                        processed_elements = await self._process_elements_for_section(elements, section, app_package)
                        elements_found = len(processed_elements)

                        console.print(f"[green]✅ XML hierarchy found {elements_found} elements[/green]")

                return {
                    "success": elements_found > 0,
                    "elements_collected": elements_found,
                    "strategy": "xml_hierarchy"
                }

            except Exception as e:
                logger.warning(f"XML hierarchy collection failed: {e}")
                return {"success": False, "elements_collected": 0, "error": str(e)}

        except Exception as e:
            logger.error(f"XML hierarchy collection failed: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _strategy_incremental_element_collection(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Strategy 3: Incremental element collection (collect what's visible, handle crashes gracefully)
        """
        try:
            console.print("[cyan]🔄 Using incremental collection strategy[/cyan]")

            elements_found = 0
            max_attempts = 3

            for attempt in range(max_attempts):
                try:
                    console.print(f"[cyan]📊 Incremental attempt {attempt + 1}/{max_attempts}[/cyan]")

                    # Quick element collection with timeout
                    elements = await asyncio.wait_for(
                        self._get_visible_elements_quickly(device),
                        timeout=10.0  # 10 second timeout
                    )

                    if elements:
                        processed_elements = await self._process_elements_for_section(elements, section, app_package)
                        elements_found += len(processed_elements)

                        console.print(f"[green]✅ Incremental attempt {attempt + 1} found {len(processed_elements)} elements[/green]")

                        # If we found elements, break
                        if elements_found > 0:
                            break

                    # Small delay between attempts
                    await asyncio.sleep(2)

                except asyncio.TimeoutError:
                    console.print(f"[yellow]⏰ Incremental attempt {attempt + 1} timed out[/yellow]")
                    continue
                except Exception as e:
                    logger.warning(f"Incremental attempt {attempt + 1} failed: {e}")
                    continue

            return {
                "success": elements_found > 0,
                "elements_collected": elements_found,
                "strategy": "incremental"
            }

        except Exception as e:
            logger.error(f"Incremental collection failed: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _strategy_emergency_collection(self, device, section: Dict, app_package: str) -> Dict[str, Any]:
        """
        Strategy 4: Emergency collection (last resort, minimal but functional)
        """
        try:
            console.print("[red]🚨 Using emergency collection strategy[/red]")

            elements_found = 0

            try:
                # Use the most basic element detection
                basic_elements = []

                # Try basic XPath selectors
                emergency_selectors = ["//*[@bounds]", "//*"]

                for selector in emergency_selectors:
                    try:
                        elements = device.xpath(selector).all()
                        if elements:
                            # Take first 50 elements to avoid overwhelming
                            basic_elements = elements[:50]
                            break
                    except:
                        continue

                if basic_elements:
                    processed_elements = await self._process_elements_for_section(basic_elements, section, app_package)
                    elements_found = len(processed_elements)

                    console.print(f"[yellow]⚠️ Emergency collection found {elements_found} elements[/yellow]")

                return {
                    "success": elements_found > 0,
                    "elements_collected": elements_found,
                    "strategy": "emergency"
                }

            except Exception as e:
                logger.warning(f"Emergency collection failed: {e}")
                return {"success": False, "elements_collected": 0, "error": str(e)}

        except Exception as e:
            logger.error(f"Emergency collection failed: {e}")
            return {"success": False, "elements_collected": 0, "error": str(e)}

    async def _process_elements_for_section(self, elements: List, section: Dict, app_package: str) -> List[Dict]:
        """
        Process and filter elements for a specific section
        """
        try:
            processed_elements = []
            section_name = section.get("name", "Unknown")

            for element in elements:
                try:
                    # Extract element data
                    element_data = await self._extract_element_data(element)

                    if element_data and self._is_relevant_element(element_data, section):
                        # Add section context
                        element_data["section"] = section_name
                        element_data["app_package"] = app_package
                        element_data["collection_timestamp"] = datetime.now().isoformat()

                        # Save to persistent storage if available
                        if self.persistent_excel_manager:
                            try:
                                action_taken = self.persistent_excel_manager.add_or_update_element(element_data)
                                processed_elements.append(element_data)

                                if action_taken == 'new':
                                    console.print(f"[green]✅ NEW element: {element_data.get('text', 'No text')[:50]}[/green]")
                                elif action_taken == 'updated':
                                    console.print(f"[yellow]🔄 UPDATED element: {element_data.get('text', 'No text')[:50]}[/yellow]")
                                else:  # skipped
                                    console.print(f"[cyan]⏭️ EXISTING element: {element_data.get('text', 'No text')[:50]}[/cyan]")

                            except Exception as save_error:
                                console.print(f"[red]❌ Failed to save element: {save_error}[/red]")
                                # Still add to processed elements to avoid losing it
                                processed_elements.append(element_data)
                        else:
                            processed_elements.append(element_data)

                except Exception as e:
                    logger.debug(f"Failed to process element: {e}")
                    continue

            return processed_elements

        except Exception as e:
            logger.error(f"Element processing failed: {e}")
            return []

    async def _extract_element_data(self, element) -> Optional[Dict[str, Any]]:
        """
        Extract comprehensive data from a UI element
        """
        try:
            element_data = {}

            # Basic attributes
            try:
                element_data["text"] = getattr(element, 'text', '') or ''
                element_data["content_desc"] = getattr(element, 'content_desc', '') or ''
                element_data["resource_id"] = getattr(element, 'resource_id', '') or ''
                element_data["class_name"] = getattr(element, 'class_name', '') or ''
                element_data["package"] = getattr(element, 'package', '') or ''
                element_data["bounds"] = str(getattr(element, 'bounds', ''))
                element_data["clickable"] = getattr(element, 'clickable', False)
                element_data["enabled"] = getattr(element, 'enabled', False)
                element_data["focusable"] = getattr(element, 'focusable', False)
                element_data["scrollable"] = getattr(element, 'scrollable', False)
            except Exception as e:
                logger.debug(f"Failed to extract basic attributes: {e}")

            # Generate element signature for deduplication
            element_data["signature"] = self._generate_element_signature(element_data)

            return element_data if element_data.get("signature") else None

        except Exception as e:
            logger.debug(f"Element data extraction failed: {e}")
            return None

    def _generate_element_signature(self, element_data: Dict[str, Any]) -> str:
        """
        Generate a unique signature for element deduplication
        """
        try:
            # Create signature from key attributes
            text = element_data.get("text", "").strip()
            resource_id = element_data.get("resource_id", "").strip()
            class_name = element_data.get("class_name", "").strip()
            bounds = element_data.get("bounds", "").strip()

            signature_parts = [text, resource_id, class_name, bounds]
            signature = "|".join(signature_parts)

            return signature if signature.strip("|") else f"element_{hash(str(element_data))}"

        except Exception as e:
            logger.debug(f"Signature generation failed: {e}")
            return f"element_{hash(str(element_data))}"

    def _is_relevant_element(self, element_data: Dict[str, Any], section: Dict) -> bool:  # noqa: ARG002
        """
        Check if an element is relevant for the current section
        """
        try:
            text = element_data.get("text", "").lower()
            content_desc = element_data.get("content_desc", "").lower()
            resource_id = element_data.get("resource_id", "").lower()
            class_name = element_data.get("class_name", "").lower()
            bounds = element_data.get("bounds", "")

            # Always include clickable elements
            if element_data.get("clickable", False):
                return True

            # Include elements with meaningful text
            if text and len(text.strip()) > 0:
                return True

            # Include elements with content description
            if content_desc and len(content_desc.strip()) > 0:
                return True

            # Include elements with resource IDs
            if resource_id and len(resource_id.strip()) > 0:
                return True

            # Include elements with class names (more inclusive)
            if class_name and len(class_name.strip()) > 0:
                return True

            # Include elements with bounds (very inclusive - almost all UI elements have bounds)
            if bounds and len(bounds.strip()) > 0:
                return True

            # Include enabled elements
            if element_data.get("enabled", False):
                return True

            # Include focusable elements
            if element_data.get("focusable", False):
                return True

            # Include scrollable elements
            if element_data.get("scrollable", False):
                return True

            return False

        except Exception as e:
            logger.debug(f"Relevance check failed: {e}")
            return False

    async def _get_visible_elements_quickly(self, device) -> List:
        """
        Quickly get visible elements with minimal processing
        """
        try:
            # Use the fastest possible element detection
            elements = device.xpath("//*[@bounds]").all()
            return elements[:100]  # Limit to first 100 elements for speed

        except Exception as e:
            logger.debug(f"Quick element detection failed: {e}")
            return []

    async def _parse_xml_hierarchy(self, xml_hierarchy: str, section: Dict) -> List:  # noqa: ARG002
        """
        Parse XML hierarchy and extract elements
        """
        try:
            import xml.etree.ElementTree as ET

            root = ET.fromstring(xml_hierarchy)
            elements = []

            # Extract all nodes with useful attributes
            for node in root.iter():
                if node.attrib:
                    elements.append(node)

            return elements

        except Exception as e:
            logger.debug(f"XML parsing failed: {e}")
            return []

    async def _create_checkpoint(self, device, section: Dict, section_index: int) -> Dict[str, Any]:
        """
        Create a checkpoint before processing a section
        """
        try:
            checkpoint = {
                "timestamp": datetime.now().isoformat(),
                "section": section["name"],
                "section_index": section_index,
                "app_state": await self._capture_app_state(device),
                "elements_count_before": self.collection_state["elements_found"]
            }

            self.checkpoints.append(checkpoint)
            self.collection_state["last_checkpoint"] = checkpoint

            console.print(f"[cyan]📍 Checkpoint created for section: {section['name']}[/cyan]")
            return checkpoint

        except Exception as e:
            logger.warning(f"Checkpoint creation failed: {e}")
            return {}

    async def _capture_app_state(self, device) -> Dict[str, Any]:
        """
        Capture current app state for recovery purposes
        """
        try:
            state = {
                "current_app": device.app_current().get("package", "unknown"),
                "screen_elements_count": len(device.xpath("//*").all()),
                "timestamp": datetime.now().isoformat()
            }
            return state

        except Exception as e:
            logger.debug(f"App state capture failed: {e}")
            return {}

    async def _attempt_section_recovery(self, device, section: Dict, checkpoint: Dict) -> Dict[str, Any]:
        """
        Attempt to recover from section failure using checkpoint
        """
        try:
            console.print(f"[yellow]🔄 Attempting recovery for section: {section['name']}[/yellow]")

            self.collection_state["recovery_count"] += 1

            # Try to restore app state
            recovery_success = await self._restore_from_checkpoint(device, checkpoint)

            if recovery_success:
                # Retry collection with emergency strategy
                recovery_result = await self._strategy_emergency_collection(device, section, checkpoint.get("app_state", {}).get("current_app", "unknown"))

                console.print(f"[green]✅ Recovery successful: {recovery_result.get('elements_collected', 0)} elements[/green]")
                return recovery_result
            else:
                console.print(f"[red]❌ Recovery failed for section: {section['name']}[/red]")
                return {"success": False, "elements_collected": 0}

        except Exception as e:
            logger.error(f"Section recovery failed: {e}")
            return {"success": False, "elements_collected": 0}

    async def _restore_from_checkpoint(self, device, checkpoint: Dict) -> bool:
        """
        Restore app state from checkpoint
        """
        try:
            # Basic recovery: ensure app is running
            target_app = checkpoint.get("app_state", {}).get("current_app", "com.kemendikdasmen.rumahpendidikan")

            current_app = device.app_current().get("package", "")
            if current_app != target_app:
                device.app_start(target_app)
                await asyncio.sleep(3)

            return True

        except Exception as e:
            logger.warning(f"Checkpoint restoration failed: {e}")
            return False

    async def _save_progress_checkpoint(self, total_elements: int, sections_completed: int):
        """
        Save progress checkpoint
        """
        try:
            self.collection_state["elements_found"] = total_elements
            self.collection_state["sections_completed_count"] = sections_completed
            self.collection_state["last_update"] = datetime.now().isoformat()

            # Save to persistent storage if available
            if self.persistent_excel_manager:
                self.persistent_excel_manager.save_to_persistent_file()
                console.print(f"[cyan]💾 Progress saved: {total_elements} elements, {sections_completed} sections[/cyan]")

        except Exception as e:
            logger.warning(f"Progress checkpoint save failed: {e}")

    async def _collect_navigation_elements_robustly(self, device, app_package: str) -> Dict[str, Any]:
        """
        Collect navigation elements robustly
        """
        try:
            console.print("[cyan]🧭 Collecting navigation elements...[/cyan]")

            navigation_section = {
                "name": "Navigation",
                "type": "navigation",
                "elements": self.menu_structure["navigation_elements"]
            }

            nav_result = await self._collect_section_elements_robustly(device, navigation_section, app_package)

            console.print(f"[green]✅ Navigation collection: {nav_result.get('elements_collected', 0)} elements[/green]")
            return nav_result

        except Exception as e:
            logger.error(f"Navigation collection failed: {e}")
            return {"success": False, "elements_collected": 0}

    async def _emergency_collection_fallback(self, device, app_package: str) -> Dict[str, Any]:
        """
        Emergency fallback collection when all else fails
        """
        try:
            console.print("[red]🚨 EMERGENCY FALLBACK COLLECTION[/red]")

            # Use the most basic collection possible
            emergency_section = {"name": "Emergency", "type": "emergency"}
            emergency_result = await self._strategy_emergency_collection(device, emergency_section, app_package)

            return {
                "success": True,
                "total_elements": emergency_result.get("elements_collected", 0),
                "collection_strategy": "emergency_fallback",
                "warning": "Emergency collection used - may be incomplete"
            }

        except Exception as e:
            logger.error(f"Emergency fallback failed: {e}")
            return {
                "success": False,
                "total_elements": 0,
                "collection_strategy": "failed",
                "error": str(e)
            }

    async def _verify_collection_completeness(self, collection_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Verify collection completeness and quality
        """
        try:
            console.print("[cyan]🔍 Verifying collection completeness...[/cyan]")

            total_elements = collection_result.get("total_elements", 0)
            sections_completed = collection_result.get("sections_completed", 0)
            total_sections = len(self.menu_structure["main_sections"])

            completion_percentage = (sections_completed / total_sections * 100) if total_sections > 0 else 0

            quality_score = self._calculate_quality_score(collection_result)

            verification_result = {
                **collection_result,
                "completion_percentage": completion_percentage,
                "quality_score": quality_score,
                "verification_timestamp": datetime.now().isoformat(),
                "collection_summary": {
                    "total_elements": total_elements,
                    "sections_completed": sections_completed,
                    "total_sections": total_sections,
                    "recovery_attempts": self.collection_state.get("recovery_count", 0)
                }
            }

            console.print(f"[green]✅ Collection verification complete: {completion_percentage:.1f}% sections, Quality: {quality_score:.1f}/10[/green]")
            return verification_result

        except Exception as e:
            logger.error(f"Collection verification failed: {e}")
            return collection_result

    def _calculate_quality_score(self, collection_result: Dict[str, Any]) -> float:
        """
        Calculate quality score for the collection
        """
        try:
            score = 0.0

            # Elements count score (0-4 points)
            total_elements = collection_result.get("total_elements", 0)
            if total_elements >= 200:
                score += 4.0
            elif total_elements >= 100:
                score += 3.0
            elif total_elements >= 50:
                score += 2.0
            elif total_elements >= 10:
                score += 1.0

            # Sections completion score (0-3 points)
            sections_completed = collection_result.get("sections_completed", 0)
            total_sections = len(self.menu_structure["main_sections"])
            completion_ratio = sections_completed / total_sections if total_sections > 0 else 0
            score += completion_ratio * 3.0

            # Strategy success score (0-2 points)
            strategy = collection_result.get("collection_strategy", "")
            if strategy == "progressive_with_checkpoints":
                score += 2.0
            elif strategy == "emergency_fallback":
                score += 1.0

            # Recovery penalty (0-1 points deduction)
            recovery_count = collection_result.get("recovery_count", 0)
            if recovery_count == 0:
                score += 1.0
            elif recovery_count <= 2:
                score += 0.5

            return min(score, 10.0)  # Cap at 10.0

        except Exception as e:
            logger.debug(f"Quality score calculation failed: {e}")
            return 5.0  # Default middle score
