"""
Analysis Monitor for Real-time AI Analysis Monitoring
Monitors analysis progress and provides real-time metrics for autonomous problem solving
"""

import asyncio
import time
import json
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from pathlib import Path
from loguru import logger

from utils.config import Config
from utils.console import get_centralized_console
from utils.terminal_logger import log_ai_analysis_event

console = get_centralized_console()

class AnalysisMonitor:
    """
    Real-time monitor for AI analysis that tracks progress, performance,
    and provides metrics for autonomous problem solving
    """
    
    def __init__(self):
        self.config = Config()
        
        # Monitoring state
        self.is_monitoring = False
        self.analysis_start_time = None
        self.last_update_time = None
        
        # Metrics tracking
        self.metrics = {
            "elements_found": 0,
            "unknown_elements": 0,
            "navigation_failures": 0,
            "device_status": "unknown",
            "duration_minutes": 0.0,
            "screens_analyzed": 0,
            "performance_score": 0.0,
            "error_count": 0,
            "last_activity": None
        }
        
        # Performance tracking
        self.performance_history = []
        self.error_history = []
        
        # Monitoring configuration
        self.monitoring_config = {
            "update_interval_seconds": self.config.get("MONITOR_UPDATE_INTERVAL", 10),
            "performance_window_minutes": self.config.get("PERFORMANCE_WINDOW_MINUTES", 5),
            "alert_thresholds": {
                "low_elements": self.config.get("TARGET_ELEMENTS_COUNT", 3000) * 0.5,
                "high_unknown_percent": self.config.get("MAX_UNKNOWN_ELEMENTS_PERCENT", 3),
                "max_duration_minutes": self.config.get("MAX_ANALYSIS_DURATION_MINUTES", 40),
                "max_navigation_failures": self.config.get("MAX_NAVIGATION_FAILURES", 5)
            }
        }
        
        # Callbacks for problem detection
        self.problem_callbacks = []
        
    async def initialize(self):
        """Initialize the analysis monitor"""
        try:
            console.print("[cyan]📊 Initializing Analysis Monitor...[/cyan]")
            
            # Reset metrics
            self.metrics = {
                "elements_found": 0,
                "unknown_elements": 0,
                "navigation_failures": 0,
                "device_status": "unknown",
                "duration_minutes": 0.0,
                "screens_analyzed": 0,
                "performance_score": 0.0,
                "error_count": 0,
                "last_activity": None
            }
            
            # Clear history
            self.performance_history = []
            self.error_history = []
            
            console.print("[green]✅ Analysis Monitor initialized successfully[/green]")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Analysis Monitor: {e}")
            return False
    
    async def start_monitoring(self, analysis_context: Dict[str, Any] = None):
        """Start monitoring the analysis"""
        try:
            self.is_monitoring = True
            self.analysis_start_time = datetime.now()
            self.last_update_time = self.analysis_start_time
            
            console.print("[cyan]📊 Starting real-time analysis monitoring...[/cyan]")
            log_ai_analysis_event("analysis_monitoring_started", "Real-time monitoring activated")
            
            # Start monitoring loop in background
            asyncio.create_task(self._monitoring_loop())
            
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop monitoring the analysis"""
        try:
            self.is_monitoring = False
            
            # Calculate final metrics
            if self.analysis_start_time:
                total_duration = datetime.now() - self.analysis_start_time
                self.metrics["duration_minutes"] = total_duration.total_seconds() / 60
            
            console.print("[yellow]⏹️ Stopped analysis monitoring[/yellow]")
            log_ai_analysis_event("analysis_monitoring_stopped", f"Total duration: {self.metrics['duration_minutes']:.1f} minutes")
            
            # Generate final report
            final_report = await self.generate_final_report()
            return final_report
            
        except Exception as e:
            logger.error(f"Failed to stop monitoring: {e}")
            return {}
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        try:
            while self.is_monitoring:
                # Update metrics
                await self._update_metrics()
                
                # Check for problems
                await self._check_for_problems()
                
                # Update performance history
                await self._update_performance_history()
                
                # Wait for next update
                await asyncio.sleep(self.monitoring_config["update_interval_seconds"])
                
        except Exception as e:
            logger.error(f"Monitoring loop failed: {e}")
    
    async def _update_metrics(self):
        """Update current analysis metrics"""
        try:
            # Update duration
            if self.analysis_start_time:
                duration = datetime.now() - self.analysis_start_time
                self.metrics["duration_minutes"] = duration.total_seconds() / 60
            
            # Update last activity
            self.metrics["last_activity"] = datetime.now().isoformat()
            
            # Calculate performance score
            self.metrics["performance_score"] = await self._calculate_performance_score()
            
        except Exception as e:
            logger.debug(f"Metrics update failed: {e}")
    
    async def _calculate_performance_score(self) -> float:
        """Calculate current performance score (0-100)"""
        try:
            score = 50.0  # Base score
            
            # Element detection performance (0-30 points)
            elements_found = self.metrics["elements_found"]
            target_elements = self.config.get("TARGET_ELEMENTS_COUNT", 3000)
            if target_elements > 0:
                element_ratio = min(elements_found / target_elements, 1.0)
                score += element_ratio * 30
            
            # Unknown elements penalty (0-20 points)
            if elements_found > 0:
                unknown_ratio = self.metrics["unknown_elements"] / elements_found
                max_unknown = self.config.get("MAX_UNKNOWN_ELEMENTS_PERCENT", 3) / 100
                if unknown_ratio <= max_unknown:
                    score += 20
                else:
                    penalty = min((unknown_ratio - max_unknown) * 100, 20)
                    score -= penalty
            
            # Duration performance (0-20 points)
            duration = self.metrics["duration_minutes"]
            target_duration = self.config.get("MAX_ANALYSIS_DURATION_MINUTES", 40)
            if duration <= target_duration:
                duration_ratio = 1.0 - (duration / target_duration)
                score += duration_ratio * 20
            
            return max(0.0, min(100.0, score))
            
        except Exception as e:
            logger.debug(f"Performance score calculation failed: {e}")
            return 50.0
    
    async def _check_for_problems(self):
        """Check for problems and notify callbacks"""
        try:
            problems = []
            thresholds = self.monitoring_config["alert_thresholds"]
            
            # Check element detection
            if self.metrics["elements_found"] > 0 and self.metrics["elements_found"] < thresholds["low_elements"]:
                problems.append({
                    "type": "low_element_detection",
                    "severity": "high",
                    "description": f"Low element count: {self.metrics['elements_found']} < {thresholds['low_elements']}",
                    "metrics": self.metrics.copy()
                })
            
            # Check unknown elements percentage
            if self.metrics["elements_found"] > 0:
                unknown_percent = (self.metrics["unknown_elements"] / self.metrics["elements_found"]) * 100
                if unknown_percent > thresholds["high_unknown_percent"]:
                    problems.append({
                        "type": "unknown_elements",
                        "severity": "medium",
                        "description": f"High unknown elements: {unknown_percent:.1f}% > {thresholds['high_unknown_percent']}%",
                        "metrics": self.metrics.copy()
                    })
            
            # Check analysis duration
            if self.metrics["duration_minutes"] > thresholds["max_duration_minutes"]:
                problems.append({
                    "type": "analysis_timeout",
                    "severity": "high",
                    "description": f"Analysis timeout: {self.metrics['duration_minutes']:.1f}min > {thresholds['max_duration_minutes']}min",
                    "metrics": self.metrics.copy()
                })
            
            # Check navigation failures
            if self.metrics["navigation_failures"] > thresholds["max_navigation_failures"]:
                problems.append({
                    "type": "navigation_failures",
                    "severity": "medium",
                    "description": f"Navigation failures: {self.metrics['navigation_failures']} > {thresholds['max_navigation_failures']}",
                    "metrics": self.metrics.copy()
                })
            
            # Notify callbacks about problems
            for problem in problems:
                await self._notify_problem_callbacks(problem)
                
        except Exception as e:
            logger.debug(f"Problem checking failed: {e}")
    
    async def _notify_problem_callbacks(self, problem: Dict[str, Any]):
        """Notify registered callbacks about detected problems"""
        try:
            for callback in self.problem_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(problem)
                    else:
                        callback(problem)
                except Exception as e:
                    logger.warning(f"Problem callback failed: {e}")
                    
        except Exception as e:
            logger.debug(f"Problem callback notification failed: {e}")
    
    async def _update_performance_history(self):
        """Update performance history for trend analysis"""
        try:
            current_time = datetime.now()
            
            # Add current performance snapshot
            performance_snapshot = {
                "timestamp": current_time.isoformat(),
                "elements_found": self.metrics["elements_found"],
                "performance_score": self.metrics["performance_score"],
                "duration_minutes": self.metrics["duration_minutes"],
                "unknown_elements": self.metrics["unknown_elements"]
            }
            
            self.performance_history.append(performance_snapshot)
            
            # Keep only recent history (within performance window)
            window_minutes = self.monitoring_config["performance_window_minutes"]
            cutoff_time = current_time - timedelta(minutes=window_minutes)
            
            self.performance_history = [
                snapshot for snapshot in self.performance_history
                if datetime.fromisoformat(snapshot["timestamp"]) > cutoff_time
            ]
            
        except Exception as e:
            logger.debug(f"Performance history update failed: {e}")
    
    def register_problem_callback(self, callback: Callable):
        """Register a callback for problem notifications"""
        self.problem_callbacks.append(callback)
    
    def update_metric(self, metric_name: str, value: Any):
        """Update a specific metric"""
        try:
            if metric_name in self.metrics:
                self.metrics[metric_name] = value
                self.last_update_time = datetime.now()
            else:
                logger.warning(f"Unknown metric: {metric_name}")
        except Exception as e:
            logger.debug(f"Metric update failed for {metric_name}: {e}")
    
    def increment_metric(self, metric_name: str, increment: int = 1):
        """Increment a numeric metric"""
        try:
            if metric_name in self.metrics and isinstance(self.metrics[metric_name], (int, float)):
                self.metrics[metric_name] += increment
                self.last_update_time = datetime.now()
            else:
                logger.warning(f"Cannot increment non-numeric metric: {metric_name}")
        except Exception as e:
            logger.debug(f"Metric increment failed for {metric_name}: {e}")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current analysis metrics"""
        return self.metrics.copy()
    
    def get_performance_trend(self) -> Dict[str, Any]:
        """Get performance trend analysis"""
        try:
            if len(self.performance_history) < 2:
                return {"trend": "insufficient_data", "history_points": len(self.performance_history)}
            
            # Calculate trend
            recent_scores = [snapshot["performance_score"] for snapshot in self.performance_history[-5:]]
            early_scores = [snapshot["performance_score"] for snapshot in self.performance_history[:5]]
            
            recent_avg = sum(recent_scores) / len(recent_scores)
            early_avg = sum(early_scores) / len(early_scores)
            
            trend_direction = "improving" if recent_avg > early_avg else "declining" if recent_avg < early_avg else "stable"
            trend_magnitude = abs(recent_avg - early_avg)
            
            return {
                "trend": trend_direction,
                "magnitude": trend_magnitude,
                "recent_average": recent_avg,
                "early_average": early_avg,
                "history_points": len(self.performance_history)
            }
            
        except Exception as e:
            logger.debug(f"Performance trend calculation failed: {e}")
            return {"trend": "error", "error": str(e)}
    
    async def generate_final_report(self) -> Dict[str, Any]:
        """Generate final monitoring report"""
        try:
            return {
                "monitoring_summary": {
                    "total_duration_minutes": self.metrics["duration_minutes"],
                    "final_performance_score": self.metrics["performance_score"],
                    "elements_found": self.metrics["elements_found"],
                    "unknown_elements": self.metrics["unknown_elements"],
                    "navigation_failures": self.metrics["navigation_failures"],
                    "error_count": self.metrics["error_count"]
                },
                "performance_trend": self.get_performance_trend(),
                "final_metrics": self.get_current_metrics(),
                "monitoring_config": self.monitoring_config,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Final report generation failed: {e}")
            return {"error": str(e)}
